<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Barber Booking App - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #263238, #37474f);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            max-width: 800px;
            padding: 40px;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #00bcd4, #4fc3f7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(0, 188, 212, 0.3);
        }
        
        .feature h3 {
            color: #00bcd4;
            margin-bottom: 10px;
        }
        
        .splash-demo {
            margin: 30px 0;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            border: 2px solid #00bcd4;
        }
        
        .loading {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ffc107;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status {
            background: rgba(255, 193, 7, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }
        
        .btn {
            background: linear-gradient(45deg, #00bcd4, #4fc3f7);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .app-info {
            text-align: left;
            margin: 20px 0;
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 10px;
        }
        
        .code-snippet {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪒 Barber Booking App</h1>
        <p class="subtitle">แอปพลิเคชันจองคิวร้านตัดผม - Flutter Application</p>
        
        <div class="splash-demo">
            <h3>🎬 Splash Screen Demo</h3>
            <p>แอปจะเริ่มต้นด้วยหน้า Splash Screen เป็นเวลา 4 วินาที</p>
            <div class="loading"></div>
            <p>กำลังโหลด...</p>
        </div>
        
        <div class="status">
            <strong>📱 สถานะปัจจุบัน:</strong> แอปกำลังอัปเดต dependencies เพื่อรองรับ Flutter เวอร์ชันใหม่
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>📅 จองคิว</h3>
                <p>ระบบจองคิวตัดผมออนไลน์</p>
            </div>
            <div class="feature">
                <h3>🗺️ แผนที่</h3>
                <p>ค้นหาร้านตัดผมใกล้เคียง</p>
            </div>
            <div class="feature">
                <h3>📋 นัดหมาย</h3>
                <p>จัดการนัดหมายและประวัติ</p>
            </div>
        </div>
        
        <div class="app-info">
            <h3>📋 ข้อมูลแอป</h3>
            <ul>
                <li><strong>ชื่อแอป:</strong> BASP (Barber App)</li>
                <li><strong>เทคโนโลยี:</strong> Flutter</li>
                <li><strong>หน้าหลัก:</strong> AppointmentScreen</li>
                <li><strong>ฟีเจอร์:</strong> Google Maps, การจัดเก็บข้อมูล, Provider State Management</li>
            </ul>
        </div>
        
        <div class="code-snippet">
            <strong>🔧 การแก้ไขที่ทำ:</strong><br>
            • อัปเดต Dart SDK เป็น ">=2.12.0 &lt;4.0.0"<br>
            • อัปเดต dependencies ให้รองรับ null safety<br>
            • อัปเดต Google Maps Flutter เป็นเวอร์ชันใหม่
        </div>
        
        <button class="btn" onclick="showAppStructure()">📁 ดูโครงสร้างแอป</button>
        <button class="btn" onclick="showFeatures()">✨ ดูฟีเจอร์ทั้งหมด</button>
        
        <div id="details" style="margin-top: 20px;"></div>
    </div>
    
    <script>
        function showAppStructure() {
            document.getElementById('details').innerHTML = `
                <div class="app-info">
                    <h3>📁 โครงสร้างแอป</h3>
                    <div class="code-snippet">
lib/<br>
├── main.dart (จุดเริ่มต้นแอป)<br>
├── screens/ (หน้าจอต่างๆ)<br>
│   ├── AppointmentScreen.dart<br>
│   ├── bookingScreen.dart<br>
│   └── mapsScreen.dart<br>
├── Provider/ (State Management)<br>
│   └── map_data_provider.dart<br>
├── Views/ (UI Components)<br>
├── core/ (Core Functions)<br>
└── Temporary_data/ (ข้อมูลชั่วคราว)
                    </div>
                </div>
            `;
        }
        
        function showFeatures() {
            document.getElementById('details').innerHTML = `
                <div class="app-info">
                    <h3>✨ ฟีเจอร์ทั้งหมด</h3>
                    <div class="features">
                        <div class="feature">
                            <h3>🎨 UI/UX</h3>
                            <p>• Dark Theme<br>• Material Design<br>• Responsive Layout</p>
                        </div>
                        <div class="feature">
                            <h3>🗺️ Maps Integration</h3>
                            <p>• Google Maps<br>• Location Services<br>• Nearby Barber Shops</p>
                        </div>
                        <div class="feature">
                            <h3>💾 Data Management</h3>
                            <p>• Secure Storage<br>• Shared Preferences<br>• Provider Pattern</p>
                        </div>
                        <div class="feature">
                            <h3>📱 Mobile Features</h3>
                            <p>• Sliding Panels<br>• Cached Images<br>• Cross Platform</p>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // Auto-show app structure after 3 seconds
        setTimeout(() => {
            showAppStructure();
        }, 3000);
    </script>
</body>
</html>
