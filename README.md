# Connect on whatsapp for customized project with admin panel and full setup :
[WhatsApp](https://wa.me/+917073708192) (DALPAT I)


# Barber-Booking-App

Barber Booking App (Part of Fashion recommendation system). You can see nearby available barbers and their services too. Book an appointment with barber on your preferred time and available slots.
Fix an appointment and Do payment with great discount.

# Try the App

This project is a part of the fashion recommendation system project, the main goal of our project is to provide an ultimate collection of all the suitable types of fashion recommendation to anyone within an app. so that no one needs to worry about fashion anymore. The App will suggest you hair styles, dresses that suite you and many more..

The source code is **100% Dart**.

### Show some :heart: and star the repo to support the project

### Some Screenshots

<img height="480px" src="https://user-images.githubusercontent.com/49696449/118353426-c4153700-b583-11eb-8b8d-23a8102a529f.png"><img height="480px" src="https://user-images.githubusercontent.com/49696449/118353414-bc559280-b583-11eb-851b-d1ed2185d9f6.png"><img height="480px" src="https://user-images.githubusercontent.com/49696449/118353429-c5466400-b583-11eb-9b21-5178d066c313.png"> <img height="480px" src="https://user-images.githubusercontent.com/49696449/118353432-c7102780-b583-11eb-8cce-cd3bd6d725b4.png"> <img height="480px" src="https://user-images.githubusercontent.com/49696449/118353435-ca0b1800-b583-11eb-9186-970def982a23.png"> <img height="480px" src="https://user-images.githubusercontent.com/49696449/118353433-c8d9eb00-b583-11eb-8a57-44327e68217b.png"> 

## Features
- Google Maps Preview
- Search box for searching locations and barber shop
- See Gallery and Reviews of other customers
- See all the Availabe services and Prices
- Book an appointment with preferred time slots from anywhere
- Do payment and get exciting offers

### The stack & building from source

The project is currently built using the latest Flutter Master, with Dart 2 enabled.

To build the project, ensure that you have a recent version of the Flutter SDK installed. Then either run `flutter run` in the project root or use your IDE of choice.

## Changes you should do :
- Change package name in `AndroidManifest` of all debug, main and profile in app\src.
- Change package name in `MainActivity.kt` also change in build.gradle.
- Change Google Maps API Key in `AndroidManifest` and `AppDelegate.swift`.

### :heart: Found this project useful?

If you found this project useful, then please consider giving it a :star: on Github and sharing it with your friends via social media.


## Getting Started
- Clone or download
- Build and Run

# Pull Requests

I welcome and encourage all pull requests. It usually will take me within 24-48 hours to respond to any issue or request. Here are some basic rules to follow to ensure timely addition of your request:

1.  Match the document style as closely as possible.
2.  Please keep PR titles easy to read and descriptive of changes, this will make them easier to merge :)
3.  Pull requests _must_ be made against `master` branch for this particular repository.
4.  Make sure you follow the set standard as all other projects in this repo do
5.  Have fun!

# Dependencies :-

		Stable internet connection


### Created & Maintained By

[Dalpat I](https://github.com/dalpat98)
([Instagram](https://www.instagram.com/dalpat_chaudhary__/))
([WhatsApp](https://wa.me/+917073708192))

> If you found this project helpful or you learned something from the source code and want to thank me, consider staring the repo. :heart:

        
<p align="center">
  <b><i>Let's connect! Find me on the web.</i></b>

[<img height="30" src="https://img.shields.io/badge/twitter-%231DA1F2.svg?&style=for-the-badge&logo=twitter&logoColor=white" />][twitter]
[<img height="30" src="https://img.shields.io/badge/linkedin-blue.svg?&style=for-the-badge&logo=linkedin&logoColor=white" />][linkedin]
[<img height="30" src = "https://img.shields.io/badge/Facebook-036be4.svg?&style=for-the-badge&logo=facebook&logoColor=white">][Facebook]
[<img height="30" src = "https://img.shields.io/badge/instagram-c14438?&style=for-the-badge&logo=instagram&logoColor=white">][instagram]
<br />
<hr />

[twitter]: https://twitter.com/DalpatDc
[linkedin]: https://www.linkedin.com/in/dalpat-i-b5b451166/
[instagram]: https://www.instagram.com/dalpat_chaudhary__/
[Facebook]: https://www.facebook.com/dalpatchaudhary.blogspot.in/

If you have any Queries or Suggestions, feel free to reach out to me.
<h3 align="center">Show some &nbsp;❤️&nbsp; by starring some of the repositories!</h3>

# License

    Copyright 2021 Dalpat I

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
